{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\comindexs\\index.vue?vue&type=style&index=0&id=23b0f17b&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\comindexs\\index.vue", "mtime": 1754026252871}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753075296083}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753075298360}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753075296703}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753075295543}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQovLyDlhoXlrrkNCi5jb250ZW50cyB7DQogIC5jb250ZXRuX2xlZnQsDQogIC5jb250ZXRuX3JpZ2h0IHsNCiAgICB3aWR0aDogNDMwcHg7DQogICAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCiAgfQ0KDQogIC5jb250ZXRuX2xlZnQgew0KICAgIGhlaWdodDogOTYwcHg7DQogICAgZ2FwOiAxMHB4Ow0KICB9DQoNCiAgLmNvbnRldG5fY2VudGVyIHsNCiAgICBoZWlnaHQ6IDk2MHB4Ow0KICAgIHdpZHRoOiAxNDM5cHg7DQogIH0NCg0KICAvL+W3puWPs+S4pOS+pyDkuInkuKrlnZcNCiAgLmNvbnRldG5fbHItaXRlbSB7DQogICAgaGVpZ2h0OiAzMTFweDsNCiAgfQ0KDQogIC5jb250ZXRuX2NlbnRlcl90b3Agew0KICAgIHdpZHRoOiAxMDAlOw0KICB9DQoNCiAgLy8g5Lit6Ze0DQogIC5jb250ZXRuX2NlbnRlciB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYXJvdW5kOw0KICB9DQoNCiAgLmNvbnRldG5fY2VudGVyLWJvdHRvbSB7DQogICAgaGVpZ2h0OiAzMTVweDsNCiAgfQ0KDQogIC8v5bem6L65IOWPs+i+uSDnu5PmnoTkuIDmoLcNCiAgLmNvbnRldG5fbGVmdCwNCiAgLmNvbnRldG5fcmlnaHQgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWFyb3VuZDsNCiAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIH0NCn0NCg0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAuYA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/comindexs", "sourcesContent": ["<!--\r\n * @Author: daidai\r\n * @Date: 2022-03-04 09:23:59\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2022-05-07 11:05:02\r\n * @FilePath: \\web-pc\\src\\pages\\big-screen\\view\\indexs\\index.vue\r\n-->\r\n<template>\r\n  <div class=\"contents\">\r\n    <div class=\"contetn_left\">\r\n      <div class=\"pagetab\">\r\n        <!-- <div class=\"item\">实时监测</div> -->\r\n        \r\n      </div>\r\n      <ItemWrap class=\"contetn_left-top contetn_lr-item\" title=\"基本信息\">\r\n        \r\n        <div style=\"height: 50px;padding-top: 10px;padding-left: 30px;font-size: 19px;\" >船名：{{ shipName }}</div>\r\n        <div style=\"height: 50px;padding-left: 30px;font-size: 19px;\">SN：{{ shipSn }}</div>\r\n        <div style=\"height: 50px;padding-left: 30px;font-size: 19px;\">经度：{{ shipLongitude + shipLongitudeH }}</div>\r\n        <div style=\"height: 50px;padding-left: 30px;font-size: 19px;\">纬度：{{ shipLatitude + shipLatitudeH }}</div>\r\n        <div style=\"height: 50px;padding-left: 30px;font-size: 19px;\">UTC：{{ shipUtc }}</div>\r\n\r\n      </ItemWrap>\r\n      <ItemWrap class=\"contetn_left-center contetn_lr-item\" title=\"姿态信息\">\r\n      \r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          船首向\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ attitudeHeading }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          横摇\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ attitudeRolling }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          纵摇\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{attitudePitch}}</div>\r\n        </div>\r\n        \r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          高度\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{attitudeHeight}}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          经度\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{formatCoordinate(attitudeLongitude)}}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          纬度\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ formatCoordinate(attitudeLatitude) }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          距离\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ attitudeDistance }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          船速\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ attitudeSpeed }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 10px;font-size: 14px;\">\r\n          更新时间\r\n          <div style=\"padding-top: 5px;font-size: 16px;\">{{ attitudeUptime }}</div>\r\n        </div>\r\n\r\n      </ItemWrap>\r\n      <ItemWrap class=\"contetn_left-bottom contetn_lr-item\" title=\"气象信息\">\r\n        <div id=\"main1\" style=\"float: left;width:180px;text-align: center;height: 200px;padding-top: 13px;padding-left: -30px;float: left;\">\r\n          \r\n        </div>\r\n        <div id=\"main2\" style=\"float: left;width:180px;text-align: center;height: 200px;padding-top: 13px;padding-left: 15px;float: left;\">\r\n          \r\n        </div>\r\n          <div style=\"padding-top: 5px;font-size: 16px;\">{{ awsUptime }}</div>\r\n      </ItemWrap>\r\n    </div>\r\n    <div class=\"contetn_center\">\r\n      <CenterMap class=\"contetn_center_top\" />\r\n      <!--\r\n      <ItemWrap class=\"contetn_center-bottom\" title=\"安装计划\">\r\n        <CenterBottom />\r\n      </ItemWrap>\r\n      -->\r\n    </div>\r\n    <!--\r\n    <div class=\"contetn_right\">\r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"报警次数\"\r\n      >\r\n        <RightTop />\r\n      </ItemWrap>\r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"报警排名(TOP8)\"\r\n        style=\"padding: 0 10px 16px 10px\"\r\n      >\r\n        <RightCenter />\r\n      </ItemWrap>\r\n      \r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"数据统计图 \"\r\n      >\r\n        <RightBottom />\r\n      </ItemWrap>\r\n    </div>\r\n    -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport LeftTop from './left-top.vue'\r\nimport LeftCenter from \"./left-center.vue\";\r\nimport LeftBottom from \"./left-bottom.vue\";\r\nimport CenterMap from \"./center-map.vue\";\r\nimport CenterBottom from \"./center-bottom.vue\";\r\nimport RightTop from \"./right-top.vue\";\r\nimport RightCenter from \"./right-center.vue\";\r\nimport RightBottom from \"./right-bottom.vue\";\r\nimport { initWebSocket, dataModule, manualClose, clearData } from '@/utils/webSocket'\r\n\r\nexport default {\r\n  components: {\r\n    LeftTop,\r\n    LeftCenter,\r\n    LeftBottom,\r\n    CenterMap,\r\n    RightTop,\r\n    RightCenter,\r\n    RightBottom,\r\n    CenterBottom,\r\n  },\r\n  data() {\r\n    return {\r\n      shipName: \"\",\r\n      shipMmsi: \"\",\r\n      shipCallSign: \"\",\r\n      shipSn: \"\",\r\n      shipLongitude: \"\",\r\n      shipLatitude: \"\",\r\n      shipLongitudeH: \"\",\r\n      shipLatitudeH: \"\",\r\n      shipUtc: \"\",\r\n      attitudeHeading: \"\",\r\n      attitudeRolling: \"\",\r\n      attitudePitch: \"\",\r\n      attitudeHeight: \"\",\r\n      attitudeLongitude: \"\",\r\n      attitudeLatitude: \"\",\r\n      attitudeDistance: \"\",\r\n      attitudeSpeed: \"\",\r\n      attitudeUptime: \"\",\r\n      awsUptime: \"\",\r\n      awsMyChart1: null,\r\n      awsMyChart2: null\r\n    };\r\n  },\r\n  filters: {\r\n    numsFilter(msg) {\r\n      return msg || 0;\r\n    },\r\n  },\r\n  created() {\r\n    this.startDataMonitoring();\r\n  },\r\n\r\n  mounted() {\r\n    //销毁其他页面连接\r\n    manualClose();\r\n\r\n    // 清空数据\r\n    clearData();\r\n\r\n    //与后端建立长连接\r\n    // 检查并恢复企业状态\r\n    this.restoreCompanyState();\r\n    initWebSocket();\r\n\r\n    var chartDom1 = document.getElementById('main1');\r\n    var myChart1 = echarts.init(chartDom1, 'dark');\r\n    var option1;\r\n    var chartDom2 = document.getElementById('main2');\r\n    var myChart2 = echarts.init(chartDom2, 'dark');\r\n    var option2;\r\n    // 气象仪图表数据\r\n    option1 = {\r\n      backgroundColor: \"#03050c\",\r\n      series: [\r\n        {\r\n          min: 0,\r\n          max: 360,\r\n          splitNumber: 4,\r\n          radius: \"100%\",\r\n          //startAngle: 90,\r\n          //endAngle: 90.0000001,\r\n          name: 'Pressure',\r\n          type: 'gauge',\r\n          progress: {\r\n            show: true\r\n          },\r\n          detail: {\r\n            valueAnimation: true,\r\n            formatter: '{value}',\r\n            offsetCenter: [0, '80%']\r\n          },\r\n          data: [\r\n            {\r\n              value: 0,\r\n              name: '风向'\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    };\r\n\r\n    option2 = {\r\n      backgroundColor: \"#03050c\",\r\n      series: [\r\n        {\r\n          min: 0,\r\n          max: 360,\r\n          splitNumber: 4,\r\n          radius: \"100%\",\r\n          //startAngle: 90,\r\n          //endAngle: 90.0000001,\r\n          name: 'Pressure',\r\n          type: 'gauge',\r\n          progress: {\r\n            show: true\r\n          },\r\n          detail: {\r\n            valueAnimation: true,\r\n            formatter: '{value}',\r\n            offsetCenter: [0, '80%']\r\n          },\r\n          data: [\r\n            {\r\n              value: 0,\r\n              name: '风速'\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    };\r\n    option1 && myChart1.setOption(option1);\r\n    option2 && myChart2.setOption(option2);\r\n    this.awsMyChart1 = myChart1;\r\n    this.awsMyChart2 = myChart2;\r\n\r\n  },\r\n  methods: {\r\n    // 转换NMEA格式的经纬度为十进制度数\r\n    convertCoordinate(coordinate, type) {\r\n      if (!coordinate) return '';\r\n\r\n      // 移除最后的方向字母（E/W/N/S）\r\n      const direction = coordinate.slice(-1);\r\n      const numericPart = coordinate.slice(0, -1);\r\n\r\n      let degrees, minutes;\r\n\r\n      if (type === 'longitude') {\r\n        // 经度格式：DDDMM.MMMMMM (前3位是度，后面是分)\r\n        degrees = parseInt(numericPart.substring(0, 3));\r\n        minutes = parseFloat(numericPart.substring(3));\r\n      } else {\r\n        // 纬度格式：DDMM.MMMMMM (前2位是度，后面是分)\r\n        degrees = parseInt(numericPart.substring(0, 2));\r\n        minutes = parseFloat(numericPart.substring(2));\r\n      }\r\n\r\n      // 转换为十进制度数\r\n      let decimal = degrees + minutes / 60;\r\n\r\n      // 根据方向确定正负\r\n      if (direction === 'W' || direction === 'S') {\r\n        decimal = -decimal;\r\n      }\r\n\r\n      // 保留6位小数\r\n      return decimal.toFixed(6);\r\n    },\r\n\r\n    // 格式化经纬度显示（保留6位小数）\r\n    formatCoordinate(coordinate) {\r\n      if (!coordinate) return '';\r\n      const num = parseFloat(coordinate);\r\n      return isNaN(num) ? coordinate : num.toFixed(6);\r\n    },\r\n\r\n    // 恢复企业状态\r\n    restoreCompanyState() {\r\n      let deptId = '101'; // 默认值\r\n      try {\r\n        const sessionCompany = sessionStorage.getItem('selectedCompany');\r\n        if (sessionCompany) {\r\n          const companyInfo = JSON.parse(sessionCompany);\r\n          if (companyInfo.sendtext) {\r\n            dataModule.sendtext = companyInfo.sendtext;\r\n            return;\r\n          }\r\n          if (companyInfo.deptId) {\r\n            deptId = companyInfo.deptId;\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.warn('读取sessionStorage中的企业信息失败:', error);\r\n      }\r\n\r\n      dataModule.sendtext = `type66#${deptId}#0#0B01,0B02,0B03,0B04,0B05,0B06`;\r\n    },\r\n\r\n    startDataMonitoring() {\r\n      this.wsCheckTimer = setInterval(() => {\r\n        this.checkWebSocketData()\r\n      }, 1000)\r\n    },\r\n    // 检查WebSocket数据\r\n    checkWebSocketData() {\r\n      if (dataModule.D0B01) {\r\n        this.shipName = dataModule.D0B01.name;\r\n        this.shipMmsi = dataModule.D0B01.mmsi;\r\n        this.shipCallSign = dataModule.D0B01.callSign;\r\n        this.shipSn = dataModule.D0B01.sn;\r\n        // 转换经纬度格式\r\n        this.shipLongitude = this.convertCoordinate(dataModule.D0B01.longitude, 'longitude')+\"°\";\r\n        this.shipLatitude = this.convertCoordinate(dataModule.D0B01.latitude, 'latitude')+\"°\";\r\n        this.shipLongitudeH = dataModule.D0B01.longitudeHemisphere;\r\n        this.shipLatitudeH = dataModule.D0B01.latitudeHemisphere;\r\n        this.shipUtc = dataModule.D0B01.utc;\r\n\r\n        this.attitudeHeading = dataModule.D0B02.attitudeHeading;\r\n        this.attitudeRolling = dataModule.D0B02.attitudeRolling;\r\n        this.attitudePitch = dataModule.D0B02.attitudePitch;\r\n        this.attitudeHeight = dataModule.D0B02.attitudeHeight;\r\n        this.attitudeLongitude = dataModule.D0B02.attitudeLongitude;\r\n        this.attitudeLatitude = dataModule.D0B02.attitudeLatitude;\r\n        this.attitudeDistance = dataModule.D0B02.attitudeDistance;\r\n        this.attitudeSpeed = dataModule.D0B02.attitudeSpeed;\r\n        this.attitudeUptime = dataModule.D0B02.attitudeUptime;\r\n        \r\n        //this.awsSpeed = dataModule.D0B03.awsSpeed;\r\n        //this.awsDirection = dataModule.D0B03.awsDirection;\r\n        if(isNaN(dataModule.D0B03.awsSpeed)){\r\n          dataModule.D0B03.awsSpeed = 0;\r\n        }\r\n        if(isNaN(dataModule.D0B03.awsDirection)){\r\n          dataModule.D0B03.awsDirection = 0;\r\n        }\r\n\r\n        this.awsMyChart1.setOption({\r\n          series: [\r\n            {\r\n              data: [\r\n                {\r\n                  value: dataModule.D0B03.awsSpeed\r\n                }\r\n              ]\r\n            }\r\n          ]\r\n        });\r\n\r\n        this.awsMyChart2.setOption({\r\n          series: [\r\n            {\r\n              data: [\r\n                {\r\n                  value: dataModule.D0B03.awsDirection\r\n                }\r\n              ]\r\n            }\r\n          ]\r\n        });\r\n\r\n      }\r\n\r\n      if (dataModule.D0B03) {\r\n        this.awsUptime = dataModule.D0B03.awsUptime;\r\n      }\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n// 内容\r\n.contents {\r\n  .contetn_left,\r\n  .contetn_right {\r\n    width: 430px;\r\n    box-sizing: border-box;\r\n  }\r\n\r\n  .contetn_left {\r\n    height: 960px;\r\n    gap: 10px;\r\n  }\r\n\r\n  .contetn_center {\r\n    height: 960px;\r\n    width: 1439px;\r\n  }\r\n\r\n  //左右两侧 三个块\r\n  .contetn_lr-item {\r\n    height: 311px;\r\n  }\r\n\r\n  .contetn_center_top {\r\n    width: 100%;\r\n  }\r\n\r\n  // 中间\r\n  .contetn_center {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-around;\r\n  }\r\n\r\n  .contetn_center-bottom {\r\n    height: 315px;\r\n  }\r\n\r\n  //左边 右边 结构一样\r\n  .contetn_left,\r\n  .contetn_right {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-around;\r\n    position: relative;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}