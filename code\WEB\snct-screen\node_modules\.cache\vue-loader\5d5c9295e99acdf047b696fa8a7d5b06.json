{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\comindexs\\index.vue?vue&type=template&id=23b0f17b&scoped=true&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\comindexs\\index.vue", "mtime": 1754026252871}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753075298398}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}