{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue", "mtime": 1754033441726}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["center-map.vue"], "names": [], "mappings": ";AAyBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "center-map.vue", "sourceRoot": "src/views/indexs", "sourcesContent": ["<!--\r\n * @Author: daidai\r\n * @Date: 2022-03-01 11:17:39\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2022-09-29 15:50:18\r\n * @FilePath: \\web-pc\\src\\pages\\big-screen\\view\\indexs\\center-map.vue\r\n-->\r\n<template>\r\n  <div class=\"centermap\">\r\n    <div class=\"maptitle\">\r\n      <div class=\"zuo\"></div>\r\n      <span class=\"titletext\">{{ maptitle }}</span>\r\n      <div class=\"you\"></div>\r\n    </div>\r\n    <div class=\"mapwrap\">\r\n      <dv-border-box-13>\r\n        <!-- 直接使用 bigemap 原生方式加载地图 -->\r\n        <div id=\"mapDiv\" style=\"width: 98%;height: 97%;margin: 10px auto;\">\r\n        </div>\r\n      </dv-border-box-13>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport BigMap from '@/components/map/BigMap.vue'\r\nimport MapControls from '@/components/map/MapControls.vue'\r\nimport { currentGET } from \"api/modules\";\r\nimport { dataModule } from '@/utils/webSocket';\r\nimport { createNineLineLayer } from '@/utils/map/nineLineData'\r\n\r\nexport default {\r\n  components: {\r\n    BigMap,\r\n    MapControls\r\n  },\r\n  data() {\r\n    return {\r\n      maptitle: \"企业数量： 家 | 接入船总数量： 艘\",\r\n      map: null, // 地图实例\r\n      mapCenter: [120.0, 30.0], // 地图中心点\r\n      mapZoom: 5, // 地图缩放级别\r\n      currentLayer: 'satellite', // 当前图层\r\n      code: \"china\", // 当前区域代码\r\n      wsCheckTimer: null, // WebSocket数据监听定时器\r\n      shipTotalCount: 0, // 船舶总数量\r\n      enterpriseTotalCount: 0, // 企业总数量\r\n      enterpriseMarkers: [], // 企业标记数组\r\n      nineLineOverlays: [], // 九段线覆盖物\r\n      regionData: [], // 区域数据\r\n    };\r\n  },\r\n  created() {},\r\n\r\n  mounted() {\r\n    this.getData(\"china\");\r\n    this.startDataMonitoring();\r\n  },\r\n  beforeDestroy() {\r\n    // 清除定时器\r\n    if (this.wsCheckTimer) {\r\n      clearInterval(this.wsCheckTimer);\r\n      this.wsCheckTimer = null;\r\n    }\r\n    // 清理地图资源\r\n    this.clearEnterpriseMarkers();\r\n  },\r\n  methods: {\r\n    // 地图准备就绪回调\r\n    onMapReady(map) {\r\n      this.map = map;\r\n      console.log('地图初始化完成');\r\n\r\n      // 添加九段线\r\n      this.addNineLine();\r\n\r\n      // 如果有区域数据，显示企业标记\r\n      if (this.regionData.length > 0) {\r\n        this.displayEnterpriseMarkers(this.regionData);\r\n      }\r\n    },\r\n\r\n    // 地图点击事件\r\n    onMapClick(event) {\r\n      console.log('地图点击:', event);\r\n      // 可以在这里添加点击交互逻辑\r\n    },\r\n\r\n    // 图层切换\r\n    onLayerChange(layer) {\r\n      this.currentLayer = layer;\r\n      if (this.$refs.bigMap) {\r\n        this.$refs.bigMap.switchLayer(layer);\r\n      }\r\n    },\r\n\r\n    // 全屏切换\r\n    onFullscreenChange(fullscreen) {\r\n      console.log('全屏状态:', fullscreen);\r\n      // 可以在这里处理全屏逻辑\r\n    },\r\n\r\n    // 添加九段线\r\n    addNineLine() {\r\n      if (this.map) {\r\n        this.nineLineOverlays = createNineLineLayer(this.map);\r\n      }\r\n    },\r\n\r\n    // 开始WebSocket数据监听\r\n    startDataMonitoring() {\r\n      this.wsCheckTimer = setInterval(() => {\r\n        this.checkWebSocketData();\r\n      }, 1000);\r\n    },\r\n\r\n    // 检查WebSocket数据\r\n    checkWebSocketData() {\r\n      if (dataModule.D0A02) {\r\n        const newData = dataModule.D0A02;\r\n        // 检查数据是否有变化\r\n        if (newData.ship_num !== this.shipTotalCount || newData.enterprise_num !== this.enterpriseTotalCount) {\r\n          this.shipTotalCount = newData.ship_num || 0;\r\n          this.enterpriseTotalCount = newData.enterprise_num || 0;\r\n\r\n          // 更新地图标题显示\r\n          this.updateMapTitle();\r\n\r\n          console.log('船舶数量数据更新:', {\r\n            ship_num: this.shipTotalCount,\r\n            enterprise_num: this.enterpriseTotalCount\r\n          });\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新地图标题\r\n    updateMapTitle() {\r\n      this.maptitle = `企业数量：${this.enterpriseTotalCount} 家 | 接入船总数量：${this.shipTotalCount} 艘`;\r\n    },\r\n\r\n    getData(code) {\r\n      currentGET(\"big8\", { regionCode: code }).then((res) => {\r\n        console.log(\"设备分布\", res);\r\n        if (res.success) {\r\n          this.code = code;\r\n          this.regionData = res.data.dataList || [];\r\n          this.displayEnterpriseMarkers(this.regionData);\r\n        } else {\r\n          this.$Message.warning(res.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 显示企业标记\r\n    displayEnterpriseMarkers(dataList) {\r\n      if (!this.map || !dataList) return;\r\n\r\n      // 清除现有标记\r\n      this.clearEnterpriseMarkers();\r\n\r\n      // 添加新标记\r\n      dataList.forEach((item, index) => {\r\n        // 这里需要根据实际数据结构调整\r\n        // 假设数据包含 name, longitude, latitude, value 等字段\r\n        if (item.longitude && item.latitude) {\r\n          const marker = this.$refs.bigMap.addMarker(\r\n            item.longitude,\r\n            item.latitude,\r\n            {\r\n              title: item.name,\r\n              content: `${item.name}: ${item.value || 0}个设备`,\r\n              icon: this.getEnterpriseIcon(item.value)\r\n            },\r\n            `enterprise_${index}`\r\n          );\r\n\r\n          if (marker) {\r\n            this.enterpriseMarkers.push(marker);\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 获取企业图标\r\n    getEnterpriseIcon(value) {\r\n      // 根据设备数量返回不同大小的图标\r\n      let size = 20;\r\n      if (value > 100) size = 32;\r\n      else if (value > 50) size = 28;\r\n      else if (value > 10) size = 24;\r\n\r\n      return {\r\n        url: '/img/markers/enterprise.png',\r\n        size: [size, size],\r\n        anchor: [size / 2, size / 2]\r\n      };\r\n    },\r\n\r\n    // 清除企业标记\r\n    clearEnterpriseMarkers() {\r\n      if (this.$refs.bigMap) {\r\n        this.enterpriseMarkers.forEach(marker => {\r\n          this.$refs.bigMap.removeMarker(marker);\r\n        });\r\n      }\r\n      this.enterpriseMarkers = [];\r\n    },\r\n\r\n    // 简化的消息提示方法\r\n    message(text) {\r\n      this.$Message({\r\n        text: text,\r\n        type: \"warning\",\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.centermap {\r\n  .maptitle {\r\n    display: flex;\r\n    justify-content: right;\r\n    margin: 25px 0 5px 0;\r\n    box-sizing: border-box;\r\n\r\n    .titletext {\r\n      font-size: 26px;\r\n      font-weight: 300;\r\n      letter-spacing: 1px;\r\n      background: linear-gradient(\r\n        92deg,\r\n        #0072ff 0%,\r\n        #00eaff 48.8525390625%,\r\n        #01aaff 100%\r\n      );\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      margin: 0 16px;\r\n    }\r\n\r\n    .zuo,\r\n    .you {\r\n      background-size: 100% 100%;\r\n      width: 26px;\r\n      height: 16px;\r\n      margin-top: 7px;\r\n    }\r\n\r\n    .zuo {\r\n      background: url(\"../../assets/img/xiezuo.png\") no-repeat;\r\n    }\r\n\r\n    .you {\r\n      background: url(\"../../assets/img/xieyou.png\") no-repeat;\r\n    }\r\n  }\r\n\r\n  .mapwrap {\r\n    height: 900px;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n\r\n    .big-map-container {\r\n      width: 100%;\r\n      height: 100%;\r\n      position: relative;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}